// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Base class for all enemy controllers, providing common functionality
    /// </summary>
    public abstract class CoreEnemyController : MonoStateMachine, IEnemyController
    {
        // UNITY HOOKUP

        [Header("Enemy Base Settings")]
        [SerializeField] protected bool _isActive = true;
        [SerializeField] protected bool _isDead = false;
        [SerializeField] protected Transform _spawnPoint;


        // OTHER FIELDS

        protected Transform _currentTarget;
        protected Vector3 _originalSpawnPosition;
        protected Quaternion _originalSpawnRotation;


        // PROPERTIES

        public GameObject GameObject => gameObject;
        public Transform Transform => transform;
        public bool IsActive => _isActive && !_isDead;
        public bool IsDead => _isDead;
        public Transform CurrentTarget => _currentTarget;


        // LIFECYCLE EVENTS

        protected virtual void Awake()
        {
            // Store original spawn position and rotation
            _originalSpawnPosition = transform.position;
            _originalSpawnRotation = transform.rotation;

            // If a spawn point is assigned, use that instead
            if (_spawnPoint != null)
            {
                _originalSpawnPosition = _spawnPoint.position;
                _originalSpawnRotation = _spawnPoint.rotation;
            }
        }

        protected override void Start()
        {
            base.Start();
            RegisterEvents();
        }

        protected virtual void OnDestroy()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        protected virtual void RegisterEvents()
        {
            // Override in derived classes to register specific events
        }

        protected virtual void UnregisterEvents()
        {
            // Override in derived classes to unregister specific events
        }


        // INTERFACE IMPLEMENTATION

        public virtual void SetActive(bool active)
        {
            _isActive = active;
            gameObject.SetActive(active);
        }

        public virtual void Kill()
        {
            _isDead = true;
            OnKilled();
        }

        public virtual void Respawn()
        {
            _isDead = false;
            transform.position = _originalSpawnPosition;
            transform.rotation = _originalSpawnRotation;
            _currentTarget = null;
            OnRespawned();
        }

        public virtual void SetTarget(Transform target)
        {
            _currentTarget = target;
            OnTargetChanged(target);
        }

        public virtual void ClearTarget()
        {
            _currentTarget = null;
            OnTargetChanged(null);
        }


        // VIRTUAL METHODS FOR DERIVED CLASSES

        /// <summary>
        /// Called when the enemy is killed
        /// </summary>
        protected virtual void OnKilled()
        {
            // Override in derived classes for specific kill behavior
        }

        /// <summary>
        /// Called when the enemy is respawned
        /// </summary>
        protected virtual void OnRespawned()
        {
            // Override in derived classes for specific respawn behavior
        }

        /// <summary>
        /// Called when the target changes
        /// </summary>
        /// <param name="newTarget">The new target (can be null)</param>
        protected virtual void OnTargetChanged(Transform newTarget)
        {
            // Override in derived classes for specific target change behavior
        }
    }
}