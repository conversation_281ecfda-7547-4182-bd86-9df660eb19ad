// Copyright Isto Inc.

using ExitGames.Client.Photon;
using Isto.Core.Beings;
using Isto.Core.Networking;
using Photon.Pun;
using Photon.Realtime;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Zenject;

namespace Isto.Core.Photon
{
    /// <summary>
    /// Handles enemy spawning for PUN2 networked games, similar to PUN2PlayerFactory but for enemies.
    /// Supports different prefab pool configurations and multiple enemy types.
    /// </summary>
    public class PUN2EnemyFactory : MonoBehaviour, IEnemyFactory
    {
        public enum PrefabPool
        {
            None = 0,
            Resources = 1,
            Project = 2,
            Addressables = 3
        }

        [System.Serializable]
        public class EnemyPrefabEntry
        {
            public string EnemyType;
            public CoreEnemyController EnemyPrefab;
        }

        // UNITY HOOKUP

        [Header("Enemy Prefabs")]
        [SerializeField] private List<EnemyPrefabEntry> _enemyPrefabs = new List<EnemyPrefabEntry>();

        [Header("Network Settings")]
        [SerializeField] private PrefabPool _networkPoolingType;

        [Header("Spawn Settings")]
        [SerializeField] private string _enemySpawnTag = "EnemySpawn";


        // INJECTION

        private INetworkManager _networkManager;
        private DiContainer _container;

        [Inject]
        public void Inject(INetworkManager networkManager,
                           DiContainer container)
        {
            _networkManager = networkManager;
            _container = container;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            switch (_networkPoolingType)
            {
                case PrefabPool.Project:
                    PhotonNetwork.PrefabPool = new ProjectAssetPool(); // not really a pool but works for prototyping
                    break;
                case PrefabPool.Addressables:
                    PhotonNetwork.PrefabPool = new AddressablesPool(); // more serious solution for performance
                    break;
                case PrefabPool.None: // in this case we won't use the system
                case PrefabPool.Resources: // in this case this is the default photon system
                default:
                    break;
            }
        }


        // INTERFACE IMPLEMENTATION

        public GameObject CreateEnemy(string enemyType)
        {
            Vector3 spawnPosition = Vector3.zero;
            Quaternion spawnRotation = Quaternion.identity;

            // Find a spawn point with the enemy spawn tag
            GameObject spawnPoint = GameObject.FindGameObjectWithTag(_enemySpawnTag);
            if (spawnPoint != null)
            {
                spawnPosition = spawnPoint.transform.position;
                spawnRotation = spawnPoint.transform.rotation;
            }

            return CreateEnemy(enemyType, spawnPosition, spawnRotation);
        }

        public GameObject CreateEnemy(string enemyType, Vector3 position, Quaternion rotation)
        {
            // Find the prefab for this enemy type
            EnemyPrefabEntry entry = _enemyPrefabs.FirstOrDefault(e => e.EnemyType == enemyType);
            if (entry == null || entry.EnemyPrefab == null)
            {
                Debug.LogError($"PUN2EnemyFactory: No prefab found for enemy type '{enemyType}'");
                return null;
            }

            GameObject enemy = null;

            if (!_networkManager.IsMultiplayerAvailable())
            {
                // Single player mode - just instantiate locally
                enemy = Instantiate(entry.EnemyPrefab.gameObject, position, rotation);
            }
            else
            {
                // Multiplayer mode - use PUN2 networking
                object[] customInitData = { enemyType, position.x, position.y, position.z };

                switch (_networkPoolingType)
                {
                    case PrefabPool.None:
                        enemy = CustomPhotonManualSpawn(entry.EnemyPrefab.gameObject, position, rotation, enemyType);
                        break;
                    case PrefabPool.Project:
#if UNITY_EDITOR
                        string path = AssetDatabase.GetAssetPath(entry.EnemyPrefab.gameObject);
                        enemy = PhotonNetwork.Instantiate(path, position, rotation, group: 0, data: customInitData);
#endif
                        break;
                    case PrefabPool.Addressables:
                    case PrefabPool.Resources:
                    default:
                        enemy = PhotonNetwork.Instantiate(entry.EnemyPrefab.name, position, rotation, group: 0, data: customInitData);
                        break;
                }
            }

            if (enemy != null)
            {
                _container.InjectGameObject(enemy);
            }

            return enemy;
        }

        public string[] GetAvailableEnemyTypes()
        {
            return _enemyPrefabs.Where(e => e.EnemyPrefab != null).Select(e => e.EnemyType).ToArray();
        }


        // NETWORK METHODS
        public GameObject CustomPhotonManualSpawn(GameObject enemyPrefab, Vector3 position, Quaternion rotation, string enemyType)
        {
            GameObject enemy = Instantiate(enemyPrefab, position, rotation);
            PhotonView photonView = enemy.GetComponent<PhotonView>();

            if (photonView == null)
            {
                Debug.LogError($"PUN2EnemyFactory: Enemy prefab '{enemyType}' must have a PhotonView component for networking");
                Destroy(enemy);
                return null;
            }

            if (PhotonNetwork.AllocateViewID(photonView))
            {
                object[] data = new object[]
                {
                    enemyType,
                    enemy.transform.position,
                    enemy.transform.rotation,
                    photonView.ViewID
                };

                RaiseEventOptions raiseEventOptions = new RaiseEventOptions
                {
                    Receivers = ReceiverGroup.Others,
                    CachingOption = EventCaching.AddToRoomCache
                };

                SendOptions sendOptions = new SendOptions
                {
                    Reliability = true
                };

                PhotonNetwork.RaiseEvent(PUN2NetworkManager.NetworkEventCodes.CUSTOM_MANUAL_INSTANTIATION,
                                         data, raiseEventOptions, sendOptions);
            }
            else
            {
                Debug.LogError("PUN2EnemyFactory: Failed to allocate a ViewId for enemy.");
                Destroy(enemy);
                return null;
            }

            return enemy;
        }
    }
}