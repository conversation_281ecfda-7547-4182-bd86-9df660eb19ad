// Copyright Isto Inc.


using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyIdleState : EnemyState
    {
        // UNITY HOOKUP



        // OTHER FIELDS

        private float _timer;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;
            _debugStateMessage = "Spider is Idling...";
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            _timer += Time.deltaTime;

            // Only master client makes decisions in networked mode
            if (_spiderController.IsNetworked && !_spiderController.IsMasterClient)
            {
                return this;
            }

            // Check for players within detection radius first
            if (_spiderController.TryFindNearestPlayerWithinRadius())
            {
                _spiderController.SetTargetToPlayer(); // Switch target to the detected player
                return _spiderController.GetState(SpiderController.EnemyEnum.Chase);
            }

            // After idle duration, start patrolling
            if (_timer >= _spiderController.IdleDuration)
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Patrol);
            }

            return this;
        }

        public override void Exit(MonoStateMachine controller)
        {
            // cleanup if needed
        }
    }
}