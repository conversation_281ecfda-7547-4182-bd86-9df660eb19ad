// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyChaseState : EnemyState
    {
        private float _timer;
        private bool _isGettingBored;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _timer = 0f;
            _isGettingBored = false;

            // Only master client finds new targets
            if (!_spiderController.IsNetworked || _spiderController.IsMasterClient)
            {
                _spiderController.FindNearestPlayer();
            }

            UpdateDebugMessage();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            // Only master client makes decisions in networked mode
            if (_spiderController.IsNetworked && !_spiderController.IsMasterClient)
            {
                UpdateDebugMessage();
                return this;
            }

            // Check if we still have a target
            if (_spiderController.CurrentTarget == null)
            {
                // Lost target, go back to patrol
                return _spiderController.GetState(SpiderController.EnemyEnum.Patrol);
            }

            // Check if close enough to attack
            if (_spiderController.DistanceToTarget <= _spiderController.AttackDistance)
            {
                return _spiderController.GetState(SpiderController.EnemyEnum.Attack);
            }

            // Check if target is too far away (lost sight)
            if (_spiderController.DistanceToTarget >= _spiderController.LoseSightDistance)
            {
                _isGettingBored = true;
            }

            if (_isGettingBored)
            {
                _timer += Time.deltaTime;
                if (_timer >= _spiderController.BoredDuration)
                {
                    // Give up chase, go back to patrol
                    _spiderController.SetTarget(null);
                    return _spiderController.GetState(SpiderController.EnemyEnum.Patrol);
                }
            }

            UpdateDebugMessage();
            return this;
        }

        private void UpdateDebugMessage()
        {
            if (_spiderController.CurrentTarget != null)
            {
                _debugStateMessage = $"Spider is Chasing target: {_spiderController.CurrentTarget.name} (Distance: {_spiderController.DistanceToTarget:F1})";
            }
            else
            {
                _debugStateMessage = "Spider is Chasing but has no target.";
            }
        }

        public override void Exit(MonoStateMachine controller)
        {
            // cleanup if needed
        }
    }
}