// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyAttackState : EnemyState
    {
        // OTHER FIELDS

        private static readonly float ATTACK_DURATION = 1.5f;

        private float _attackTimer;


        // LIFECYCLE EVENTS

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);
            _attackTimer = 0f;
            _spiderController.SetIsAttacking(true);

            _debugStateMessage = $"Spider is Attacking {_spiderController.CurrentTarget.name}";
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (!_spiderController.IsAttacking)
            {
                return this;
            }

            // Check if target is still within attack range during the attack
            if (_spiderController.DistanceToTarget > _spiderController.AttackDistance)
            {
                if (_spiderController.IsDebugging)
                {
                    _debugStateMessage = $"Spider is Attacking {_spiderController.CurrentTarget.name}";
                    Debug.Log("Target moved out of range during attack, returning to chase");
                }
                return _spiderController.GetState(SpiderController.EnemyEnum.Chase);
            }

            // Update attack timer
            _attackTimer += Time.deltaTime;

            // Check if attack duration has completed
            if (_attackTimer >= ATTACK_DURATION)
            {
                if (_spiderController.IsDebugging)
                {
                    Debug.Log("Attack completed, returning to chase");
                }
                return _spiderController.GetState(SpiderController.EnemyEnum.Chase);
            }

            // Continue attacking
            return this;
        }

        public override void Exit(MonoStateMachine controller)
        {
            _spiderController.SetIsAttacking(false);

            if (_spiderController.IsDebugging)
            {
                Debug.Log("Spider finished attacking");
            }
        }
    }
}