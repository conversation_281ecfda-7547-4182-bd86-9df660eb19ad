// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.StateMachine;
using RootMotion.Demos;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Photon.Pun;
using System.Collections;

namespace Isto.TRP.Enemies
{
    public class SpiderController : CoreEnemyController, IPunObservable
    {
        // UNITY HOOKUP

        [Header("-- SPIDER DEBUG --")]
        [SerializeField] private Canvas _debugBillboard;
        [SerializeField] private TextMeshProUGUI _debugEnemyStateText;
        [SerializeField] private bool _isDebugging = false;

        [Header("Spider Settings")]
        [SerializeField] private MechSpider _mechSpider;
        [SerializeField] private float _speed     = 6f;
        [SerializeField] private float _turnSpeed = 60f;

        [Header("State Setup")]
        [SerializeField] private EnemyIdleState _idleState;
        [SerializeField] private EnemyPatrolState _patrolState;
        [SerializeField] private EnemyChaseState _chaseState;
        [SerializeField] private EnemyAttackState _attackState;
        [SerializeField] private EnemyRetreatState _retreatState;
        [SerializeField] private EnemyScarePlayerState _scarePlayerState;
        [SerializeField] private EnemyDeadState _deadState;

        [Header("Spider Variables")]
        [Tooltip("Which tag to look for when hunting players")]
        [SerializeField] private string _playerTag = "Player";
        [Tooltip("How far the spider can 'see'")]
        [SerializeField] private float _detectionRadius = 60;
        [SerializeField] private float _loseSightDistance = 85f;
        [SerializeField] private float _attackDistance    = 15f;
        [SerializeField] private float _boredDuration = 10f;
        [SerializeField] private float _idleDuration = 5f;
        [SerializeField] private float _patrolDuration = 45f;

        public enum EnemyEnum
        {
            Idle,
            Patrol,
            Chase,
            Attack,
            Retreat,
            ScarePlayer,
            Dead
        }

        // Used for debugging purposes
        private Transform _localPlayerCamera;
        private float _lastCameraSearchTime;
        private const float CAMERA_SEARCH_INTERVAL = 1f; // Search for camera every 1 second if not found

        private Dictionary<EnemyEnum, MonoState> _stateMap;
        private List<Transform> _patrolPoints;
        // private Transform _target;
        private Transform _playerTarget; // Separate field for tracking player targets
        private bool _isAttacking = false;
        private Transform _patrolPointContainer;

        // NETWORKING FIELDS
        private PhotonView _photonView;
        private EnemyEnum _networkedCurrentState = EnemyEnum.Idle;
        private int _networkedTargetIndex = -1; // Index of current target in patrol points list
        private int _networkedPlayerTargetViewID = -1; // PhotonView ID of player target
        private float _networkedStateTimer = 0f;
        private bool _isNetworkInitialized = false;


        // public Transform TargetTransform => _target;

        public bool IsDebugging => _isDebugging;
        public float DistanceToTarget => CurrentTarget != null ? Vector3.Distance(transform.position, CurrentTarget.position) : 0f;
        public float LoseSightDistance => _loseSightDistance;
        public float AttackDistance => _attackDistance;
        public float BoredDuration => _boredDuration;
        public float IdleDuration => _idleDuration;
        public bool IsAttacking => _isAttacking;
        public float PatrolDuration => _patrolDuration;
        public List<Transform> PatrolPoints => _patrolPoints;

        // NETWORKING PROPERTIES
        public bool IsMasterClient => _photonView != null ? _photonView.IsMine && PhotonNetwork.IsMasterClient : true;
        public bool IsNetworked => _photonView != null && PhotonNetwork.IsConnected;
        public EnemyEnum NetworkedCurrentState => _networkedCurrentState;

        // DEBUG PROPERTIES
        public string NetworkDebugInfo => $"IsMaster: {IsMasterClient}, IsNetworked: {IsNetworked}, State: {_networkedCurrentState}, Target: {_networkedTargetIndex}, PlayerTarget: {_networkedPlayerTargetViewID}";


        // UNITY LIFECYCLE

        // build the map once
        protected void Awake()
        {
            // Initialize networking
            _photonView = GetComponent<PhotonView>();

            _stateMap = new Dictionary<EnemyEnum, MonoState>
            {
                { EnemyEnum.Idle,           _idleState   },
                { EnemyEnum.Patrol,         _patrolState },
                { EnemyEnum.Chase,          _chaseState  },
                { EnemyEnum.Attack,         _attackState },
                { EnemyEnum.Retreat,        _retreatState},
                { EnemyEnum.ScarePlayer,    _scarePlayerState},
                { EnemyEnum.Dead,           _deadState   },
            };

            _patrolPointContainer = GameObject.Find("c_PatrolPoints").transform;
            _patrolPoints = new List<Transform>();
            foreach (Transform child in _patrolPointContainer)
            {
                _patrolPoints.Add(child);
            }

            _debugBillboard.gameObject.SetActive(_isDebugging);

            // Initialize camera search
            if (_isDebugging)
            {
                FindLocalPlayerCamera();
            }
        }

        protected override void Start()
        {
            base.Start();

            // Delay initialization to ensure all network components are ready
            StartCoroutine(InitializeAfterNetworkReady());
        }

        private IEnumerator InitializeAfterNetworkReady()
        {
            // Wait for network to be fully initialized
            yield return new WaitForSeconds(0.1f);

            _isNetworkInitialized = true;

            // Initialize network state after all components are ready
            if (IsNetworked)
            {
                if (IsMasterClient)
                {
                    // Master client starts in Idle state and broadcasts it
                    Debug.Log("Spider Master Client: Starting in Idle state");
                    _networkedCurrentState = EnemyEnum.Idle;
                    base.ChangeState(_stateMap[EnemyEnum.Idle]);

                    // Broadcast initial state to other clients
                    yield return new WaitForSeconds(0.1f); // Small delay to ensure clients are ready
                    if (_photonView != null)
                    {
                        _photonView.RPC("OnNetworkStateChanged", RpcTarget.Others, (int)EnemyEnum.Idle, _networkedTargetIndex, _networkedPlayerTargetViewID, _networkedStateTimer);
                    }
                }
                else
                {
                    // Non-master clients wait for state sync
                    Debug.Log("Spider Client: Waiting for state sync from master");
                    StartCoroutine(WaitForNetworkInitialization());
                }
            }
            else
            {
                // Single player mode - start normally
                Debug.Log("Spider Single Player: Starting in Idle state");
                base.ChangeState(_stateMap[EnemyEnum.Idle]);
            }
        }

        private IEnumerator WaitForNetworkInitialization()
        {
            float waitTime = 0f;
            const float maxWaitTime = 2f;

            // Wait for initial network sync or timeout
            while (waitTime < maxWaitTime && _currentState == null)
            {
                yield return new WaitForSeconds(0.1f);
                waitTime += 0.1f;
            }

            // If we still haven't received a state, default to Idle
            if (_currentState == null)
            {
                Debug.Log("Spider Client: Timeout waiting for state sync, defaulting to Idle");
                base.ChangeState(_stateMap[EnemyEnum.Idle]);
            }
        }

        // run the state‐machine first, then do movement
        protected override void Update()
        {
            if (IsDebugging)
            {
                DrawDetectionRays();
                var currentState = _currentState as EnemyState;
                string stateMessage = currentState?.DebugStateMessage ?? "No State";
                string networkInfo = IsNetworked ? $"\n{NetworkDebugInfo}" : "\nSingle Player";
                _debugEnemyStateText.text = stateMessage + networkInfo;
            }

            // Custom state machine update with networking support
            UpdateStateMachine();

            if(CurrentTarget == null || _isAttacking)
            {
                return;
            }

            Vector3 dir = (CurrentTarget.position - transform.position).normalized;
            if (dir.sqrMagnitude > 0f)
            {
                Quaternion lookRot = Quaternion.LookRotation(dir);
                transform.rotation = Quaternion.RotateTowards(
                    transform.rotation,
                    lookRot,
                    Time.deltaTime * _turnSpeed
                );
                transform.Translate(
                    Vector3.forward * _speed * Time.deltaTime * _mechSpider.scale,
                    Space.Self
                );

                if(IsDebugging)
                {
                    // Have the billboard always face the local player's camera
                    UpdateBillboardRotation();
                }
            }
        }

        /// <summary>
        /// Custom state machine update that handles networking
        /// </summary>
        private void UpdateStateMachine()
        {
            if (_currentState == null) return;

            // Run the current state
            MonoState nextState = _currentState.Run(this);

            // Check if we need to transition to a new state
            if (nextState != null && nextState != _currentState)
            {
                // Find which enum state this corresponds to
                EnemyEnum? targetEnum = null;
                foreach (var kvp in _stateMap)
                {
                    if (kvp.Value == nextState)
                    {
                        targetEnum = kvp.Key;
                        break;
                    }
                }

                // If we found the enum, use our networked state change
                if (targetEnum.HasValue)
                {
                    ChangeState(targetEnum.Value);
                }
                else
                {
                    // Fallback to direct state change (shouldn't happen)
                    Debug.LogWarning("Spider: State transition bypassed networking - state not found in map");
                    base.ChangeState(nextState);
                }
            }
        }

        public void ChangeState(EnemyEnum newState)
        {
            // Only master client can initiate state changes in networked mode
            if (IsNetworked && !IsMasterClient)
            {
                Debug.Log($"Spider (Client): Ignoring state change request to {newState} - not master client");
                return;
            }

            Debug.Log($"Spider (Master): Changing state to {newState} from {_currentState?.GetType().Name ?? "null"}");

            // Update networked state BEFORE changing the actual state
            _networkedCurrentState = newState;

            // Change the actual state
            base.ChangeState(_stateMap[newState]);

            // Broadcast state change to other clients
            if (IsNetworked && IsMasterClient)
            {
                Debug.Log($"Spider (Master): Broadcasting state change {newState} to clients");
                _photonView.RPC("OnNetworkStateChanged", RpcTarget.Others, (int)newState, _networkedTargetIndex, _networkedPlayerTargetViewID, _networkedStateTimer);
            }
        }

        [PunRPC]
        private void OnNetworkStateChanged(int stateInt, int targetIndex, int playerTargetViewID, float stateTimer)
        {
            EnemyEnum newState = (EnemyEnum)stateInt;
            _networkedCurrentState = newState;
            _networkedTargetIndex = targetIndex;
            _networkedPlayerTargetViewID = playerTargetViewID;
            _networkedStateTimer = stateTimer;

            // Apply the target based on type
            Transform targetToSet = null;

            // Check for player target first
            if (playerTargetViewID > 0)
            {
                PhotonView playerPhotonView = PhotonView.Find(playerTargetViewID);
                if (playerPhotonView != null)
                {
                    targetToSet = playerPhotonView.transform;
                    Debug.Log($"Spider received state change: {newState}, player target: {targetToSet.name}");
                }
                else
                {
                    Debug.LogWarning($"Spider: Could not find player with ViewID {playerTargetViewID}");
                }
            }
            // Check for patrol point target
            else if (targetIndex >= 0 && targetIndex < _patrolPoints.Count)
            {
                targetToSet = _patrolPoints[targetIndex];
                Debug.Log($"Spider received state change: {newState}, patrol target: {targetToSet.name}");
            }
            else
            {
                Debug.Log($"Spider received state change: {newState}, no target");
            }

            // Set the target (this will update our internal tracking)
            SetTarget(targetToSet);

            // Change to the new state directly (bypass the networked check)
            base.ChangeState(_stateMap[newState]);
        }

        public MonoState GetState(EnemyEnum newState)
        {
            return _stateMap[newState];
        }

        /// <summary>
        /// Scans all GameObjects with tag=playerTag, picks the closest one within detectionRadius.
        /// Does not overwrite the current target - only stores the player target separately.
        /// </summary>
        public bool TryFindNearestPlayerWithinRadius()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = _detectionRadius * _detectionRadius;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 <= bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _playerTarget = best;
            if (best != null)
            {
                Debug.Log($"Spider: Found player within radius: {best.name}, Tag: {best.tag}");
            }
            return best != null;
        }

        public void FindNearestPlayer()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = float.MaxValue;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 < bestSqr)
                {
                    bestSqr = d2;
                    best    = go.transform;
                }
            }
            _playerTarget = best;
            SetTarget(best); // Use SetTarget method to properly handle networking
        }

        public new void SetTarget(Transform newTarget)
        {
            // Also update the base class target
            base.SetTarget(newTarget);

            // Update networked target information
            if (newTarget != null)
            {
                // Check if it's a patrol point
                if (_patrolPoints != null)
                {
                    _networkedTargetIndex = _patrolPoints.IndexOf(newTarget);
                }
                else
                {
                    _networkedTargetIndex = -1;
                }

                // Check if it's a player (has PhotonView)
                PhotonView targetPhotonView = newTarget.GetComponent<PhotonView>();
                if (targetPhotonView != null)
                {
                    _networkedPlayerTargetViewID = targetPhotonView.ViewID;
                    Debug.Log($"Spider: Set target to player {newTarget.name}, ViewID: {_networkedPlayerTargetViewID}, Tag: {newTarget.tag}");
                }
                else
                {
                    _networkedPlayerTargetViewID = -1;
                    Debug.Log($"Spider: Set target to {newTarget.name} (no PhotonView), patrol index: {_networkedTargetIndex}, Tag: {newTarget.tag}");
                }
            }
            else
            {
                _networkedTargetIndex = -1;
                _networkedPlayerTargetViewID = -1;
                Debug.Log("Spider: Set target to null");
            }
        }

        /// <summary>
        /// Sets target by patrol point index - used for network synchronization
        /// </summary>
        public void SetTargetByIndex(int patrolPointIndex)
        {
            if (patrolPointIndex >= 0 && patrolPointIndex < _patrolPoints.Count)
            {
                SetTarget(_patrolPoints[patrolPointIndex]);
            }
            else
            {
                SetTarget(null);
            }
        }

        /// <summary>
        /// Gets a deterministic patrol point index for network synchronization
        /// </summary>
        public int GetRandomPatrolPointIndex(int excludeIndex = -1)
        {
            if (_patrolPoints == null || _patrolPoints.Count == 0)
                return -1;

            if (_patrolPoints.Count == 1)
                return 0;

            // Use a deterministic method based on current time and position
            // This ensures all clients get the same "random" result
            int seed = Mathf.FloorToInt(Time.time * 1000) + transform.position.GetHashCode();
            UnityEngine.Random.State oldState = UnityEngine.Random.state;
            UnityEngine.Random.InitState(seed);

            List<int> availableIndices = new List<int>();
            for (int i = 0; i < _patrolPoints.Count; i++)
            {
                if (i != excludeIndex)
                {
                    availableIndices.Add(i);
                }
            }

            int randomIndex = availableIndices.Count > 0 ?
                availableIndices[UnityEngine.Random.Range(0, availableIndices.Count)] : 0;

            UnityEngine.Random.state = oldState;
            return randomIndex;
        }

        /// <summary>
        /// Sets the target to the currently detected player if one exists.
        /// Used when transitioning from patrol to chase state.
        /// </summary>
        public void SetTargetToPlayer()
        {
            if (_playerTarget != null)
            {
                SetTarget(_playerTarget); // Use SetTarget method to properly handle networking
            }
        }

        private void DrawDetectionRays()
        {
            int rayCount = 36;
            float step = 360f / rayCount;

            for (int i = 0; i < rayCount; i++)
            {
                float angle = step * i * Mathf.Deg2Rad;
                Vector3 dir = new Vector3(Mathf.Cos(angle), 0f, Mathf.Sin(angle));
                Debug.DrawRay(
                    transform.position,
                    dir * _detectionRadius,
                    Color.red
                );
            }
        }

        /// <summary>
        /// Finds and caches the local player's camera transform for billboard rotation.
        /// This method searches for the local player (PhotonView.IsMine == true) and gets their camera.
        /// </summary>
        private void FindLocalPlayerCamera()
        {
            // Only search if we don't have a camera reference or enough time has passed
            if (_localPlayerCamera != null || Time.time - _lastCameraSearchTime < CAMERA_SEARCH_INTERVAL)
            {
                return;
            }

            _lastCameraSearchTime = Time.time;

            // Find all TRPPlayerController instances in the scene
            TRPPlayerController[] playerControllers = FindObjectsOfType<TRPPlayerController>();

            foreach (TRPPlayerController playerController in playerControllers)
            {
                // Check if this is the local player
                if (playerController.IsMine)
                {
                    // Get the camera transform from the local player
                    _localPlayerCamera = playerController.CameraRoot;
                    break;
                }
            }

            // Fallback to Camera.main if no local player found (for testing or single-player scenarios)
            if (_localPlayerCamera == null && Camera.main != null)
            {
                _localPlayerCamera = Camera.main.transform;
            }
        }

        /// <summary>
        /// Updates the billboard rotation to face the local player's camera.
        /// Uses smooth rotation for better visual quality and performance.
        /// </summary>
        private void UpdateBillboardRotation()
        {
            // Ensure we have a camera reference
            if (_localPlayerCamera == null)
            {
                FindLocalPlayerCamera();
                return;
            }

            // Calculate direction from billboard to camera
            Vector3 directionToCamera = (_localPlayerCamera.position - _debugBillboard.transform.position).normalized;

            // Create rotation that looks at the camera
            Quaternion targetRotation = Quaternion.LookRotation(directionToCamera);

            // Apply smooth rotation for better visual quality
            _debugBillboard.transform.rotation = Quaternion.Slerp(
                _debugBillboard.transform.rotation,
                targetRotation,
                Time.deltaTime * 5f // Smooth rotation speed
            );
        }

        public void SetIsAttacking(bool isAttacking)
        {
            _isAttacking = isAttacking;
        }


        // CORE ENEMY CONTROLLER OVERRIDES

        // protected override void OnTargetChanged(Transform newTarget)
        // {
        //     base.OnTargetChanged(newTarget);
        //     _target = newTarget;
        // }

#if UNITY_EDITOR
        // Draw a wireframe sphere in the editor
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, _detectionRadius);
        }
#endif


        // IPUNOBSERVABLE IMPLEMENTATION

        public void OnPhotonSerializeView(PhotonStream stream, PhotonMessageInfo info)
        {
            if (stream.IsWriting)
            {
                // Master client sends data to other clients
                stream.SendNext((int)_networkedCurrentState);
                stream.SendNext(_networkedTargetIndex);
                stream.SendNext(_networkedPlayerTargetViewID);
                stream.SendNext(_networkedStateTimer);
            }
            else
            {
                // Non-master clients receive data from master client
                int stateInt = (int)stream.ReceiveNext();
                int targetIndex = (int)stream.ReceiveNext();
                int playerTargetViewID = (int)stream.ReceiveNext();
                float stateTimer = (float)stream.ReceiveNext();

                // Only apply if we're not the master client and network is initialized
                if (!IsMasterClient && _isNetworkInitialized)
                {
                    EnemyEnum newState = (EnemyEnum)stateInt;

                    // Always update the networked values
                    _networkedTargetIndex = targetIndex;
                    _networkedPlayerTargetViewID = playerTargetViewID;
                    _networkedStateTimer = stateTimer;

                    // Determine and apply target
                    Transform targetToSet = null;

                    // Check for player target first
                    if (playerTargetViewID > 0)
                    {
                        PhotonView playerPhotonView = PhotonView.Find(playerTargetViewID);
                        if (playerPhotonView != null)
                        {
                            targetToSet = playerPhotonView.transform;
                        }
                    }
                    // Check for patrol point target
                    else if (targetIndex >= 0 && targetIndex < _patrolPoints.Count)
                    {
                        targetToSet = _patrolPoints[targetIndex];
                    }

                    // Apply target if it's different
                    if (CurrentTarget != targetToSet)
                    {
                        SetTarget(targetToSet);
                    }

                    // Change state if different
                    if (_networkedCurrentState != newState)
                    {
                        _networkedCurrentState = newState;
                        Debug.Log($"Spider continuous sync: changing to {newState}");
                        base.ChangeState(_stateMap[newState]);
                    }
                }
            }
        }
    }
}