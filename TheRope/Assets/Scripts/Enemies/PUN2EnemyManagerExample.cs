// Copyright Isto Inc.

using Isto.Core.Beings;
using Photon.Pun;
using UnityEngine;
using Zenject;

namespace Isto.Core.Photon
{
    /// <summary>
    /// Example script showing how to use the EnemyManager with PUN2EnemyFactory for networked enemy spawning
    /// </summary>
    public class PUN2EnemyManagerExample : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("PUN2 Enemy Example Settings")]
        [SerializeField] private string _enemyTypeToSpawn = "Spider";
        [SerializeField] private bool _onlyMasterClientSpawns = true;
        [SerializeField] private bool _autoSpawnOnRoomJoin = true;


        // INJECTION

        private EnemyManager _enemyManager;
        private PUN2NetworkManager _networkManager;

        [Inject]
        public void Inject(EnemyManager enemyManager,
                          PlayerManager playerManager,
                          PUN2NetworkManager networkManager)
        {
            _enemyManager = enemyManager;
            _networkManager = networkManager;
        }


        // LIFECYCLE EVENTS

        private void Start()
        {
            RegisterEvents();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.Subscribe(Events.NETWORK_ROOM_JOINED, OnNetworkRoomJoined);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(Events.NETWORK_ROOM_JOINED, OnNetworkRoomJoined);
        }

        private void OnNetworkRoomJoined()
        {
            if (_autoSpawnOnRoomJoin && ShouldSpawnEnemies())
            {
                // Delay spawning slightly to ensure all players are properly connected
                Invoke(nameof(SpawnInitialEnemies), 1f);
            }
        }


        // ENEMY SPAWNING METHODS

        private bool ShouldSpawnEnemies()
        {
            if (!_networkManager.IsMultiplayerAvailable())
            {
                return true; // Single player mode
            }

            if (_onlyMasterClientSpawns)
            {
                return PhotonNetwork.IsMasterClient;
            }

            return true; // Allow any client to spawn
        }

        private void SpawnInitialEnemies()
        {
            if (!ShouldSpawnEnemies())
                return;

            SpawnSpiderEnemy();
        }

        private void SpawnSpiderEnemy()
        {
            // Spawn the enemy (will be networked if using PUN2EnemyFactory)
            GameObject enemy = _enemyManager.CreateEnemy(_enemyTypeToSpawn);

            if (enemy != null)
            {
                string networkStatus = _networkManager.IsMultiplayerAvailable() ? "networked" : "local";
                Debug.Log($"Spawned {networkStatus} {_enemyTypeToSpawn}");
            }
            else
            {
                Debug.LogError($"Failed to spawn enemy of type {_enemyTypeToSpawn}");
            }
        }
    }

}