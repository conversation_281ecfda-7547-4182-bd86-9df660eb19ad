// Copyright Isto Inc.

using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Manages all enemy instances in the game, similar to PlayerManager
    /// </summary>
    public class EnemyManager : MonoBehaviour
    {
        // OTHER FIELDS

        private List<IEnemyController> _allEnemyInstances;


        // PROPERTIES

        public virtual List<IEnemyController> AllEnemies => _allEnemyInstances;
        public virtual int TotalEnemyCount => _allEnemyInstances.Count;


        // INJECTION

        private IEnemyFactory _enemyFactory;

        [Inject]
        public void Inject(IEnemyFactory enemyFactory)
        {
            _enemyFactory = enemyFactory;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _allEnemyInstances = new List<IEnemyController>();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        private void Start()
        {
            // Find any existing enemies in the scene and register them
            IEnemyController[] existingEnemies = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                .OfType<IEnemyController>()
                .ToArray();

            foreach (IEnemyController enemy in existingEnemies)
            {
                RegisterEnemy(enemy);
            }

            RegisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.SubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
        }

        private void Events_OnGameObjectSpawned(object[] args)
        {
            GameObject spawnedObj = (GameObject)args[0];
            IEnemyController enemyComponent = spawnedObj.GetComponent<IEnemyController>();
            if (enemyComponent != null)
            {
                OnEnemySpawned(enemyComponent);
            }
        }

        protected virtual void OnEnemySpawned(IEnemyController enemyController)
        {
            RegisterEnemy(enemyController);
        }


        // ENEMY MANAGEMENT METHODS

        public virtual GameObject CreateEnemy(string enemyType)
        {
            GameObject enemy = _enemyFactory.CreateEnemy(enemyType);
            if (enemy != null)
            {
                IEnemyController enemyController = enemy.GetComponent<IEnemyController>();
                if (enemyController != null)
                {
                    RegisterEnemy(enemyController);
                }
            }
            else
            {
                Debug.LogError($"EnemyManager: Failed to create enemy of type {enemyType}");
            }

            return enemy;
        }

        public virtual GameObject CreateEnemy(string enemyType, Vector3 position, Quaternion rotation)
        {
            GameObject enemy = _enemyFactory.CreateEnemy(enemyType, position, rotation);
            if (enemy != null)
            {
                IEnemyController enemyController = enemy.GetComponent<IEnemyController>();
                if (enemyController != null)
                {
                    RegisterEnemy(enemyController);
                }
            }
            else
            {
                Debug.LogError($"EnemyManager: Failed to create enemy of type {enemyType} at {position}");
            }

            return enemy;
        }

        /// <summary>
        /// Register an enemy with the manager
        /// </summary>
        /// <param name="enemy">The enemy to register</param>
        protected virtual void RegisterEnemy(IEnemyController enemy)
        {
            if (!_allEnemyInstances.Contains(enemy))
            {
                _allEnemyInstances.Add(enemy);
            }
        }

        /// <summary>
        /// Unregister an enemy from the manager
        /// </summary>
        /// <param name="enemy">The enemy to unregister</param>
        protected virtual void UnregisterEnemy(IEnemyController enemy)
        {
            _allEnemyInstances.Remove(enemy);
        }

        /// <summary>
        /// Set all enemies to target a specific transform
        /// </summary>
        /// <param name="target">The target to pursue</param>
        public virtual void SetAllEnemiesTarget(Transform target)
        {
            foreach (IEnemyController enemy in _allEnemyInstances)
            {
                enemy.SetTarget(target);
            }
        }
    }
}