// Copyright Isto Inc.

using UnityEngine;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Interface for all enemy controllers to provide common functionality. Currently we only have the
    /// target that the enemy is moving to. This can be a patrol point, the player or some other transform.
    /// </summary>
    public interface IEnemyController
    {
        // /// <summary>
        // /// The current target this enemy is pursuing (if any)
        // /// </summary>
        Transform CurrentTarget { get; }

        /// <summary>
        /// Set a new target for this enemy to pursue
        /// </summary>
        /// <param name="target">The target transform</param>
        void SetTarget(Transform target);
    }
}